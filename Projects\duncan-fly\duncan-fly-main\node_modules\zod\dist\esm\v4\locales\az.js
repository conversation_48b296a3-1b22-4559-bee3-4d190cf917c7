import * as util from "../core/util.js";
const Sizable = {
    string: { unit: "simvol", verb: "olmalıdır" },
    file: { unit: "bayt", verb: "olmalıdır" },
    array: { unit: "element", verb: "olmalıdır" },
    set: { unit: "element", verb: "olmalıdır" },
};
function getSizing(origin) {
    return Sizable[origin] ?? null;
}
export const parsedType = (data) => {
    const t = typeof data;
    switch (t) {
        case "number": {
            return Number.isNaN(data) ? "NaN" : "number";
        }
        case "object": {
            if (Array.isArray(data)) {
                return "array";
            }
            if (data === null) {
                return "null";
            }
            if (Object.getPrototypeOf(data) !== Object.prototype && data.constructor) {
                return data.constructor.name;
            }
        }
    }
    return t;
};
const Nouns = {
    regex: "input",
    email: "email address",
    url: "URL",
    emoji: "emoji",
    uuid: "UUID",
    uuidv4: "UUIDv4",
    uuidv6: "UUIDv6",
    nanoid: "nanoid",
    guid: "GUID",
    cuid: "cuid",
    cuid2: "cuid2",
    ulid: "ULID",
    xid: "XID",
    ksuid: "KSUID",
    datetime: "ISO datetime",
    date: "ISO date",
    time: "ISO time",
    duration: "ISO duration",
    ipv4: "IPv4 address",
    ipv6: "IPv6 address",
    cidrv4: "IPv4 range",
    cidrv6: "IPv6 range",
    base64: "base64-encoded string",
    base64url: "base64url-encoded string",
    json_string: "JSON string",
    e164: "E.164 number",
    jwt: "JWT",
    template_literal: "input",
};
const error = (issue) => {
    switch (issue.code) {
        case "invalid_type":
            return `Yanlış dəyər: gözlənilən ${issue.expected}, daxil olan ${parsedType(issue.input)}`;
        case "invalid_value":
            if (issue.values.length === 1)
                return `Yanlış dəyər: gözlənilən ${util.stringifyPrimitive(issue.values[0])}`;
            return `Yanlış seçim: aşağıdakılardan biri olmalıdır: ${util.joinValues(issue.values, "|")}`;
        case "too_big": {
            const adj = issue.inclusive ? "<=" : "<";
            const sizing = getSizing(issue.origin);
            if (sizing)
                return `Çox böyük: gözlənilən ${issue.origin ?? "dəyər"} ${adj}${issue.maximum.toString()} ${sizing.unit ?? "element"}`;
            return `Çox böyük: gözlənilən ${issue.origin ?? "dəyər"} ${adj}${issue.maximum.toString()}`;
        }
        case "too_small": {
            const adj = issue.inclusive ? ">=" : ">";
            const sizing = getSizing(issue.origin);
            if (sizing)
                return `Çox kiçik: gözlənilən ${issue.origin} ${adj}${issue.minimum.toString()} ${sizing.unit}`;
            return `Çox kiçik: gözlənilən ${issue.origin} ${adj}${issue.minimum.toString()}`;
        }
        case "invalid_format": {
            const _issue = issue;
            if (_issue.format === "starts_with")
                return `Yanlış mətn: "${_issue.prefix}" ilə başlamalıdır`;
            if (_issue.format === "ends_with")
                return `Yanlış mətn: "${_issue.suffix}" ilə bitməlidir`;
            if (_issue.format === "includes")
                return `Yanlış mətn: "${_issue.includes}" daxil olmalıdır`;
            if (_issue.format === "regex")
                return `Yanlış mətn: ${_issue.pattern} şablonuna uyğun olmalıdır`;
            return `Yanlış ${Nouns[_issue.format] ?? issue.format}`;
        }
        case "not_multiple_of":
            return `Yanlış ədəd: ${issue.divisor} ilə bölünə bilən olmalıdır`;
        case "unrecognized_keys":
            return `Tanınmayan açar${issue.keys.length > 1 ? "lar" : ""}: ${util.joinValues(issue.keys, ", ")}`;
        case "invalid_key":
            return `${issue.origin} daxilində yanlış açar`;
        case "invalid_union":
            return "Yanlış dəyər";
        case "invalid_element":
            return `${issue.origin} daxilində yanlış dəyər`;
        default:
            return `Yanlış dəyər`;
    }
};
export { error };
export default function () {
    return {
        localeError: error,
    };
}
