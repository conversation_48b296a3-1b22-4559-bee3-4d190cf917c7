import axios from 'axios'

interface DeepseekMessage {
  role: 'system' | 'user' | 'assistant'
  content: string
}

interface DeepseekResponse {
  id: string
  object: string
  created: number
  model: string
  choices: Array<{
    index: number
    message: {
      role: string
      content: string
    }
    finish_reason: string
  }>
  usage: {
    prompt_tokens: number
    completion_tokens: number
    total_tokens: number
  }
}

class DeepseekService {
  private apiKey: string
  private apiUrl: string
  private systemPrompt: string

  constructor() {
    this.apiKey = process.env.DEEPSEEK_API_KEY || ''
    this.apiUrl = process.env.DEEPSEEK_API_URL || 'https://api.deepseek.com/v1/chat/completions'
    
    this.systemPrompt = `You are a helpful bookkeeping assistant for DuncanFly Bookkeeping. 
    You help potential clients understand our services and pricing.

    About DuncanFly:
    - We provide professional bookkeeping services for small businesses and entrepreneurs
    - Our services include monthly bookkeeping, tax preparation support, financial reporting, expense tracking, and payroll processing
    - We integrate with QuickBooks, Xero, Wave, and other major accounting software
    - Pricing starts at $299/month for basic services
    - We have 20+ years of experience and serve over 2,600 businesses

    Pricing Tiers:
    1. Essentials ($299/mo): Monthly bookkeeping, financial statements, expense tracking, tax filing support
    2. Growing ($599/mo): Everything in Essentials plus cash flow forecasting, budget planning, custom reporting
    3. Established ($999/mo): Everything in Growing plus CFO advisory services, strategic planning, investor reporting

    Always be helpful, professional, and guide users toward scheduling a consultation when appropriate.
    Format your responses using markdown for better readability.
    Keep responses concise but informative.`
  }

  async getChatCompletion(
    messages: DeepseekMessage[], 
    temperature: number = 0.7,
    maxTokens: number = 500
  ): Promise<string> {
    try {
      const response = await axios.post<DeepseekResponse>(
        this.apiUrl,
        {
          model: "deepseek-chat",
          messages: [
            { role: "system", content: this.systemPrompt },
            ...messages
          ],
          temperature,
          max_tokens: maxTokens,
          top_p: 1,
          frequency_penalty: 0,
          presence_penalty: 0,
          stream: false
        },
        {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`,
            'Content-Type': 'application/json',
          }
        }
      )

      return response.data.choices[0]?.message?.content || "I apologize, but I couldn't generate a response. Please try again."
    } catch (error) {
      console.error('Deepseek API Error:', error)
      throw new Error('Failed to get response from Deepseek')
    }
  }

  // Helper method to maintain conversation context
  async getChatResponseWithContext(
    conversationHistory: Array<{ role: 'user' | 'assistant', content: string }>,
    newMessage: string
  ): Promise<string> {
    // Convert conversation history to Deepseek format
    const messages: DeepseekMessage[] = conversationHistory.map(msg => ({
      role: msg.role,
      content: msg.content
    }))
    
    // Add the new user message
    messages.push({ role: 'user', content: newMessage })

    // Limit context to last 10 messages to avoid token limits
    const contextMessages = messages.slice(-10)

    return await this.getChatCompletion(contextMessages)
  }
}

export const deepseekService = new DeepseekService()
