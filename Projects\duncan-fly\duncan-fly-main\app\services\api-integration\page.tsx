import Header from '../../../components/Header'
import Footer from '../../../components/Footer'
import SEO from '../../../components/SEO'
import Breadcrumbs from '../../../components/Breadcrumbs'

export default function APIIntegration() {
  return (
    <>
      <SEO
        title="API Integration | DuncanFly Bookkeeping"
        description="DuncanFly Bookkeeping offers expert API integration services for QuickBooks Online and other platforms. Automate your workflows and connect your business tools for seamless bookkeeping."
        keywords="API integration, QuickBooks Online API, bookkeeping automation, cloud accounting, DuncanFly"
        canonicalUrl="https://duncanfly.com/services/api-integration"
      />
      <Header />
      <Breadcrumbs />
      <section className="gradient-bg py-20 min-h-screen">
        <div className="container mx-auto px-6">
          <div className="glass-effect rounded-2xl p-10 shadow-2xl max-w-3xl mx-auto">
            <h1 className="text-4xl font-bold mb-8 text-gray-800 text-center">API Integration</h1>
            <p className="text-lg text-gray-700 mb-6 text-center">
              Our API integration services help you connect QuickBooks Online with your favorite business tools, automate data flows, and eliminate manual entry. We specialize in secure, reliable integrations for small businesses.
            </p>
            <ul className="list-disc pl-8 text-gray-700 mb-6">
              <li>Connect QuickBooks Online to CRMs, e-commerce, and payment platforms</li>
              <li>Automate invoice creation, payment tracking, and reporting</li>
              <li>Custom API solutions for unique business needs</li>
              <li>Secure data transfer and compliance</li>
              <li>Ongoing support and troubleshooting</li>
            </ul>
            <p className="text-lg text-gray-700 text-center">
              Want to automate your bookkeeping with API integration? <a href="/contact" className="text-teal-600 underline">Contact us</a> for a free consultation.
            </p>
          </div>
        </div>
      </section>
      <Footer />
    </>
  )
}
