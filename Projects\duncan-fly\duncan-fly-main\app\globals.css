@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --primary-teal: #4ECDC4;
  --primary-dark: #1A2332;
  --accent-coral: #FF6B6B;
  --text-dark: #2D3748;
  --text-light: #718096;
  --bg-light: #F7FAFC;
  --bg-mint: #E6F7F5;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  color: var(--text-dark);
  line-height: 1.6;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.gradient-bg {
  background: linear-gradient(135deg, var(--bg-mint) 0%, #ffffff 100%);
}

.glass-effect {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

.float-animation {
  animation: float 6s ease-in-out infinite;
}
