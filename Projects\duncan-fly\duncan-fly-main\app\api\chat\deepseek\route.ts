import { NextRequest, NextResponse } from 'next/server'
import { deepseekService } from '../../../../lib/deepseek'

export async function POST(request: NextRequest) {
  try {
    const { message, conversationHistory } = await request.json()

    if (!message) {
      return NextResponse.json(
        { error: 'Message is required' },
        { status: 400 }
      )
    }

    // Check if API key is configured
    if (!process.env.DEEPSEEK_API_KEY) {
      console.error('Deepseek API key not configured')
      // Fallback to rule-based responses
      const fallbackResponse = getFallbackResponse(message)
      return NextResponse.json({ response: fallbackResponse })
    }

    // Get response from Deepseek
    const response = await deepseekService.getChatResponseWithContext(
      conversationHistory || [],
      message
    )

    return NextResponse.json({ response })
  } catch (error) {
    console.error('Chat API Error:', error)
    
    // Fallback to rule-based response if Deepseek fails
    const fallbackResponse = getFallbackResponse(request.body?.message || '')
    return NextResponse.json({ response: fallbackResponse })
  }
}

// Fallback responses when API is unavailable
function getFallbackResponse(message: string): string {
  const lowerMessage = message.toLowerCase()
  
  if (lowerMessage.includes('price') || lowerMessage.includes('cost')) {
    return `Our bookkeeping services start at **$299/month**. We offer three plans:

• **Essentials** ($299/mo): Perfect for small businesses
• **Growing** ($599/mo): For scaling businesses
• **Established** ($999/mo): Full CFO services

Would you like to schedule a free consultation to discuss which plan is right for you?`
  }
  
  if (lowerMessage.includes('service') || lowerMessage.includes('what do you offer')) {
    return `DuncanFly provides comprehensive bookkeeping services:

📊 Monthly bookkeeping & reconciliation
📈 Financial statements & reporting
💰 Tax preparation support
💳 Expense tracking & categorization
🏦 Payroll processing
📱 Real-time financial insights

We integrate with QuickBooks, Xero, and other major platforms. What specific service interests you?`
  }
  
  if (lowerMessage.includes('quickbooks') || lowerMessage.includes('xero')) {
    return `Yes! We're certified partners with both QuickBooks and Xero. We can:

✅ Set up your accounting software
✅ Import existing data
✅ Train your team
✅ Handle all bookkeeping within your platform

Which software are you currently using or considering?`
  }
  
  return `Thanks for your message! I'd be happy to help you learn more about DuncanFly's bookkeeping services. 

You can ask me about:
• Our services and pricing
• Software integrations
• How we work with businesses like yours

Or would you prefer to schedule a free consultation with our team?`
}
