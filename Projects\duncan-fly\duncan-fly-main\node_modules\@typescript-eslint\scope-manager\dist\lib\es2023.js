"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib @typescript-eslint/repo-tools
Object.defineProperty(exports, "__esModule", { value: true });
exports.es2023 = void 0;
const es2022_1 = require("./es2022");
const es2023_array_1 = require("./es2023.array");
const es2023_collection_1 = require("./es2023.collection");
exports.es2023 = {
    ...es2022_1.es2022,
    ...es2023_array_1.es2023_array,
    ...es2023_collection_1.es2023_collection,
};
//# sourceMappingURL=es2023.js.map