'use client'
import { motion } from 'framer-motion'
import Image from 'next/image'
import Link from 'next/link'

export default function Hero() {
  return (
    <section className="relative min-h-screen flex items-center gradient-bg overflow-hidden">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <motion.div
          animate={{
            scale: [1, 1.2, 1],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute -top-20 -right-20 w-96 h-96 bg-teal-200 rounded-full opacity-20 blur-3xl"
        />
        <motion.div
          animate={{
            scale: [1.2, 1, 1.2],
            rotate: [360, 180, 0],
          }}
          transition={{
            duration: 25,
            repeat: Infinity,
            ease: "linear"
          }}
          className="absolute -bottom-20 -left-20 w-96 h-96 bg-blue-200 rounded-full opacity-20 blur-3xl"
        />
      </div>

      <div className="container mx-auto px-6 relative z-10">
        <div className="flex flex-col lg:flex-row items-center justify-between">
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            className="lg:w-1/2 mb-12 lg:mb-0"
          >
            <h1 className="text-5xl lg:text-6xl font-bold text-gray-800 mb-6 leading-tight">
              Bookkeeping That Keeps You Focused on What Matters
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              DuncanFly provides comprehensive bookkeeping services tailored for small businesses. 
              Let us handle your finances while you focus on growing your business.
            </p>
            <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
              <Link href="/services">
                <motion.button
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  className="bg-gray-800 text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-gray-700 transition"
                >
                  Explore Services
                </motion.button>
              </Link>
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="bg-teal-500 text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-teal-600 transition"
              >
                Talk to an Expert
              </motion.button>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="lg:w-1/2 relative"
          >
            <div className="relative">
              {/* Floating profile images */}
              <motion.div
                animate={{ y: [0, -20, 0] }}
                transition={{ duration: 4, repeat: Infinity }}
                className="absolute top-0 right-0 w-20 h-20 rounded-full overflow-hidden border-4 border-white shadow-lg"
              >
                <Image src="https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=200" alt="Team member" width={80} height={80} className="object-cover" />
              </motion.div>
              <motion.div
                animate={{ y: [0, 20, 0] }}
                transition={{ duration: 5, repeat: Infinity }}
                className="absolute top-20 left-0 w-24 h-24 rounded-full overflow-hidden border-4 border-white shadow-lg"
              >
                <Image src="https://images.unsplash.com/photo-1560250097-0b93528c311a?w=200" alt="Team member" width={96} height={96} className="object-cover" />
              </motion.div>
              <motion.div
                animate={{ y: [0, -15, 0] }}
                transition={{ duration: 3.5, repeat: Infinity }}
                className="absolute bottom-10 right-20 w-20 h-20 rounded-full overflow-hidden border-4 border-white shadow-lg"
              >
                <Image src="https://images.unsplash.com/photo-1580894732444-8ecded7900cd?w=200" alt="Team member" width={80} height={80} className="object-cover" />
              </motion.div>
              <motion.div
                animate={{ y: [0, 15, 0] }}
                transition={{ duration: 4.5, repeat: Infinity }}
                className="absolute bottom-0 left-20 w-16 h-16 rounded-full overflow-hidden border-4 border-white shadow-lg"
              >
                <Image src="https://images.unsplash.com/photo-1598257006458-087169a1f08d?w=200" alt="Team member" width={64} height={64} className="object-cover" />
              </motion.div>

              {/* Main image */}
              <div className="glass-effect rounded-2xl p-8 shadow-2xl">
                <Image 
                  src="https://images.unsplash.com/photo-1554224155-6726b3ff858f?w=600" 
                  alt="Financial Dashboard" 
                  width={600} 
                  height={400} 
                  className="rounded-lg"
                />
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
