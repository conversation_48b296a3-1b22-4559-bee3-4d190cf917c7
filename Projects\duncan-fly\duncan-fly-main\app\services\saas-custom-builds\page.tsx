import Header from '../../../components/Header'
import Footer from '../../../components/Footer'
import SEO from '../../../components/SEO'
import Breadcrumbs from '../../../components/Breadcrumbs'

export default function SaaSCustomBuilds() {
  return (
    <>
      <SEO
        title="SaaS Custom Builds | DuncanFly Bookkeeping"
        description="Discover our SaaS custom build services for small businesses. We help you integrate, automate, and scale your bookkeeping and accounting workflows with custom cloud solutions."
        keywords="SaaS custom builds, cloud bookkeeping, automation, QuickBooks integration, DuncanFly"
        canonicalUrl="https://duncanfly.com/services/saas-custom-builds"
      />
      <Header />
      <Breadcrumbs />
      <section className="gradient-bg py-20 min-h-screen">
        <div className="container mx-auto px-6">
          <div className="glass-effect rounded-2xl p-10 shadow-2xl max-w-3xl mx-auto">
            <h1 className="text-4xl font-bold mb-8 text-gray-800 text-center">SaaS Custom Builds</h1>
            <p className="text-lg text-gray-700 mb-6 text-center">
              DuncanFly Bookkeeping offers custom SaaS solutions to automate and optimize your accounting and business processes. Our team specializes in building cloud-based tools that integrate seamlessly with QuickBooks Online and other platforms.
            </p>
            <ul className="list-disc pl-8 text-gray-700 mb-6">
              <li>Custom dashboards and reporting tools</li>
              <li>Automated data sync between apps</li>
              <li>Workflow automation for recurring tasks</li>
              <li>Secure cloud storage and document management</li>
              <li>Integration with payment processors, CRMs, and more</li>
            </ul>
            <p className="text-lg text-gray-700 text-center">
              Ready to streamline your bookkeeping with a custom SaaS solution? <a href="/contact" className="text-teal-600 underline">Contact us</a> for a free consultation.
            </p>
          </div>
        </div>
      </section>
      <Footer />
    </>
  )
}
