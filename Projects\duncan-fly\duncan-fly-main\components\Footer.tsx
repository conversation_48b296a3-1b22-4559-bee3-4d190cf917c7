import Link from 'next/link'
import { Facebook, Linkedin, Twitter, Instagram } from 'lucide-react'

export default function Footer() {
  return (
    <footer className="bg-gray-900 text-white pt-16 pb-8">
      <div className="container mx-auto px-6">
        <div className="grid md:grid-cols-3 gap-8 mb-12">
          {/* About Section */}
          <div>
            <h5 className="text-lg font-bold mb-4">ABOUT</h5>
            <ul className="space-y-2">
              <li><Link href="/about" className="text-gray-400 hover:text-white">About Us</Link></li>
              <li><Link href="/contact" className="text-gray-400 hover:text-white">Contact</Link></li>
              <li><Link href="/privacy" className="text-gray-400 hover:text-white">Privacy Policy</Link></li>
              <li><Link href="/terms" className="text-gray-400 hover:text-white">Terms & Conditions</Link></li>
            </ul>
          </div>
          {/* Services Section */}
          <div>
            <h5 className="text-lg font-bold mb-4">SERVICES</h5>
            <ul className="space-y-2">
              <li><Link href="/services" className="text-gray-400 hover:text-white">Bookkeeping</Link></li>
              <li><Link href="/services" className="text-gray-400 hover:text-white">SaaS Custom Builds</Link></li>
              <li><Link href="/services" className="text-gray-400 hover:text-white">API Integration</Link></li>
            </ul>
          </div>
          {/* Newsletter Section */}
          <div>
            <h5 className="text-lg font-bold mb-4">NEWSLETTER</h5>
            <p className="text-gray-400 mb-4">Stay updated with financial tips and DuncanFly news</p>
            <form className="flex">
              <input 
                type="email" 
                placeholder="Your email"
                className="bg-gray-800 text-white px-4 py-2 rounded-l-full flex-1 focus:outline-none"
              />
              <button className="bg-teal-500 px-6 py-2 rounded-r-full hover:bg-teal-600 transition">
                Subscribe
              </button>
            </form>
          </div>
        </div>
        
        <div className="border-t border-gray-800 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="mb-4 md:mb-0">
              <p className="text-gray-400">© 2024 DuncanFly Bookkeeping. All rights reserved.</p>
              <div className="flex space-x-4 mt-2">
                <Link href="/privacy" className="text-gray-400 hover:text-white text-sm">Privacy Policy</Link>
                <Link href="/terms" className="text-gray-400 hover:text-white text-sm">Terms of Service</Link>
              </div>
            </div>
            
            <div className="flex space-x-4">
              <a href="https://facebook.com/duncanfly" className="text-gray-400 hover:text-white">
                <Facebook size={20} />
              </a>
              <a href="https://linkedin.com/company/duncanfly" className="text-gray-400 hover:text-white">
                <Linkedin size={20} />
              </a>
              <a href="https://twitter.com/duncanfly" className="text-gray-400 hover:text-white">
                <Twitter size={20} />
              </a>
              <a href="https://instagram.com/duncanfly" className="text-gray-400 hover:text-white">
                <Instagram size={20} />
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
