'use client'
import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import { Briefcase, BarChart3, Users } from 'lucide-react'

const supportTypes = [
  {
    icon: Briefcase,
    title: "Essentials",
    description: "For when you're setting the foundation with tax compliance and core financial services.",
    features: [
      "Monthly bookkeeping",
      "Financial statements",
      "Expense tracking",
      "Tax filing support"
    ]
  },
  {
    icon: BarChart3,
    title: "Growing",
    description: "For when you're ready to leverage financial data to drive smarter business decisions.",
    features: [
      "Everything in Essentials",
      "Cash flow forecasting",
      "Budget planning",
      "Custom reporting"
    ]
  },
  {
    icon: Users,
    title: "Established",
    description: "For when you need advanced financial management to scale your established business.",
    features: [
      "Everything in Growing",
      "CFO advisory services",
      "Strategic planning",
      "Investor reporting"
    ]
  }
]

export default function Support() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  })

  return (
    <section ref={ref} className="py-20 gradient-bg">
      <div className="container mx-auto px-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl font-bold text-gray-800 mb-4">
            Support for your business at every stage.
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Over the past 20 years, we've identified 3 key stages to meet the needs of your business as it evolves.
          </p>
        </motion.div>

        <div className="grid md:grid-cols-3 gap-8">
          {supportTypes.map((type, index) => (
            <motion.div
              key={type.title}
              initial={{ opacity: 0, y: 30 }}
              animate={inView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.6, delay: index * 0.2 }}
              className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow"
            >
              <div className="w-16 h-16 bg-gradient-to-br from-teal-400 to-teal-600 rounded-2xl flex items-center justify-center mb-6">
                <type.icon className="w-8 h-8 text-white" />
              </div>
              <h3 className="text-2xl font-bold text-gray-800 mb-4">{type.title}</h3>
              <p className="text-gray-600 mb-6">{type.description}</p>
              <ul className="space-y-3">
                {type.features.map((feature) => (
                  <li key={feature} className="flex items-center text-gray-700">
                    <div className="w-2 h-2 bg-teal-500 rounded-full mr-3"></div>
                    {feature}
                  </li>
                ))}
              </ul>
              <button className="mt-8 w-full bg-gray-100 text-gray-800 py-3 rounded-full hover:bg-gray-200 transition">
                Learn More
              </button>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="text-center mt-16 bg-white rounded-2xl p-12 shadow-xl"
        >
          <h3 className="text-3xl font-bold text-gray-800 mb-4">
            Ready to get started?
          </h3>
          <p className="text-xl text-gray-600 mb-8">
            See how DuncanFly can help you focus on what matters most.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="bg-gray-800 text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-gray-700 transition">
              View Pricing
            </button>
            <button className="bg-teal-500 text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-teal-600 transition">
              Get Started Free
            </button>
          </div>
        </motion.div>
      </div>
    </section>
  )
}
