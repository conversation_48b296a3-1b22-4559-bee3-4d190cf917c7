'use client'
import Link from 'next/link'
import { usePathname } from 'next/navigation'

export default function Breadcrumbs() {
  const pathname = usePathname()
  const segments = pathname.split('/').filter(Boolean)
  let path = ''

  return (
    <nav className="text-sm text-gray-500 mb-6" aria-label="Breadcrumb">
      <ol className="flex flex-wrap items-center space-x-2">
        <li>
          <Link href="/" className="hover:text-teal-600 font-semibold">Home</Link>
        </li>
        {segments.map((seg, i) => {
          path += '/' + seg
          const isLast = i === segments.length - 1
          const label = seg
            .replace(/-/g, ' ')
            .replace(/\b\w/g, l => l.toUpperCase())
          return (
            <li key={path} className="flex items-center">
              <span className="mx-2">/</span>
              {isLast ? (
                <span className="text-gray-700 font-semibold">{label}</span>
              ) : (
                <Link href={path} className="hover:text-teal-600">{label}</Link>
              )}
            </li>
          )
        })}
      </ol>
    </nav>
  )
}
