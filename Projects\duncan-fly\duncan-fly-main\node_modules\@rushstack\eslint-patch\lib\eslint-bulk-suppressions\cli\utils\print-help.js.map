{"version": 3, "file": "print-help.js", "sourceRoot": "", "sources": ["../../../../src/eslint-bulk-suppressions/cli/utils/print-help.ts"], "names": [], "mappings": ";AAAA,4FAA4F;AAC5F,2DAA2D;;AAI3D,wCAaC;AAED,8BA8BC;AAED,8CA6BC;AA9ED,+DAAyD;AAEzD,SAAgB,cAAc;IAC5B,MAAM,IAAI,GAAW;;;;;;sNAM+L,CAAC;IAErN,MAAM,OAAO,GAAa,IAAA,sCAAgB,EAAC,IAAI,CAAC,CAAC;IACjD,KAAK,MAAM,IAAI,IAAI,OAAO,EAAE,CAAC;QAC3B,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACpB,CAAC;AACH,CAAC;AAED,SAAgB,SAAS;IACvB,MAAM,IAAI,GAAW;;;;;;;;;;;;;;;;;;;;;;;CAuBtB,CAAC;IAEA,MAAM,OAAO,GAAa,IAAA,sCAAgB,EAAC,IAAI,CAAC,CAAC;IACjD,KAAK,MAAM,IAAI,IAAI,OAAO,EAAE,CAAC;QAC3B,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACpB,CAAC;AACH,CAAC;AAED,SAAgB,iBAAiB;IAC/B,MAAM,IAAI,GAAW;;;;;;;;;;;;;;;;;;;;;;4DAsBqC,CAAC;IAE3D,MAAM,OAAO,GAAa,IAAA,sCAAgB,EAAC,IAAI,CAAC,CAAC;IACjD,KAAK,MAAM,IAAI,IAAI,OAAO,EAAE,CAAC;QAC3B,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;IACpB,CAAC;AACH,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license.\n// See <PERSON><PERSON>EN<PERSON> in the project root for license information.\n\nimport { wrapWordsToLines } from './wrap-words-to-lines';\n\nexport function printPruneHelp(): void {\n  const help: string = `eslint-bulk prune\n\nUsage:\n\neslint-bulk prune\n\nThis command is a thin wrapper around E<PERSON><PERSON> that communicates with @rushstack/eslint-patch to delete all unused suppression entries in all .eslint-bulk-suppressions.json files under the current working directory.`;\n\n  const wrapped: string[] = wrapWordsToLines(help);\n  for (const line of wrapped) {\n    console.log(line);\n  }\n}\n\nexport function printHelp(): void {\n  const help: string = `eslint-bulk <command>\n\nUsage:\n\neslint-bulk suppress --rule RULENAME1 [--rule RULENAME2...] PATH1 [PATH2...]\neslint-bulk suppress --all PATH1 [PATH2...]\neslint-bulk suppress --help\n\neslint-bulk prune\neslint-bulk prune --help\n\neslint-bulk --help\n\nThis command line tool is a thin wrapper around <PERSON>SLint that communicates with @rushstack/eslint-patch to suppress or prune unused suppressions in the local .eslint-bulk-suppressions.json file.\n\nCommands:\n  eslint-bulk suppress [options] <path...>\n    Use this command to generate a new .eslint-bulk-suppressions.json file or add suppression entries to the existing file. Specify the files and rules you want to suppress.\n    Please run \"eslint-bulk suppress --help\" to learn more.\n\n  eslint-bulk prune\n    Use this command to delete all unused suppression entries in all .eslint-bulk-suppressions.json files under the current working directory.\n    Please run \"eslint-bulk prune --help\" to learn more.\n`;\n\n  const wrapped: string[] = wrapWordsToLines(help);\n  for (const line of wrapped) {\n    console.log(line);\n  }\n}\n\nexport function printSuppressHelp(): void {\n  const help: string = `eslint-bulk suppress [options] <path...>\n\nUsage:\n\neslint-bulk suppress --rule RULENAME1 [--rule RULENAME2...] PATH1 [PATH2...]\neslint-bulk suppress --all PATH1 [PATH2...]\neslint-bulk suppress --help\n\nThis command is a thin wrapper around ESLint that communicates with @rushstack/eslint-patch to either generate a new .eslint-bulk-suppressions.json file or add suppression entries to the existing file. Specify the files and rules you want to suppress.\n\nArgument:\n  <path...>\n    Glob patterns for paths to suppress, same as eslint files argument. Should be relative to the project root.\n\nOptions:\n  -h, -H, --help\n    Display this help message.\n\n  -R, --rule\n    The full name of the ESLint rule you want to bulk-suppress. Specify multiple rules with --rule NAME1 --rule RULENAME2.\n\n  -A, --all\n    Bulk-suppress all rules in the specified file patterns.`;\n\n  const wrapped: string[] = wrapWordsToLines(help);\n  for (const line of wrapped) {\n    console.log(line);\n  }\n}\n"]}