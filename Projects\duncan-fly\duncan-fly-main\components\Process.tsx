'use client'
import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import { CheckCircle, ArrowRight } from 'lucide-react'

const steps = [
  {
    number: "01",
    title: "Connect Your Accounts",
    description: "Securely link your bank accounts, credit cards, and financial tools. Our encrypted connections ensure your data stays safe.",
    features: ["256-bit encryption", "Read-only access", "Automatic syncing"]
  },
  {
    number: "02",
    title: "We Handle Your Books",
    description: "Our expert bookkeepers categorize transactions, reconcile accounts, and maintain accurate records following industry best practices.",
    features: ["Daily transaction review", "Monthly reconciliation", "Expense categorization"]
  },
  {
    number: "03",
    title: "Get Financial Insights",
    description: "Receive monthly financial statements, tax-ready reports, and actionable insights to make informed business decisions.",
    features: ["P&L statements", "Balance sheets", "Cash flow analysis"]
  }
]

export default function Process() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  })

  return (
    <section ref={ref} className="py-20 gradient-bg">
      <div className="container mx-auto px-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl font-bold text-gray-800 mb-4">
            How DuncanFly Works
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Getting started with professional bookkeeping has never been easier. 
            Our streamlined process gets you up and running in days, not weeks.
          </p>
        </motion.div>

        <div className="grid md:grid-cols-3 gap-8 relative">
          {/* Connection lines for desktop */}
          <div className="hidden md:block absolute top-20 left-1/3 right-1/3 h-0.5 bg-teal-200"></div>
          
          {steps.map((step, index) => (
            <motion.div
              key={step.number}
              initial={{ opacity: 0, y: 30 }}
              animate={inView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.6, delay: index * 0.2 }}
              className="relative"
            >
              <div className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-shadow">
                <div className="flex items-center justify-between mb-6">
                  <span className="text-5xl font-bold text-teal-500">{step.number}</span>
                  {index < steps.length - 1 && (
                    <ArrowRight className="text-teal-300 hidden md:block" size={24} />
                  )}
                </div>
                <h3 className="text-2xl font-bold text-gray-800 mb-4">{step.title}</h3>
                <p className="text-gray-600 mb-6">{step.description}</p>
                <ul className="space-y-2">
                  {step.features.map((feature) => (
                    <li key={feature} className="flex items-center text-gray-700">
                      <CheckCircle className="w-5 h-5 text-teal-500 mr-2" />
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>
            </motion.div>
          ))}
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="text-center mt-12"
        >
          <button className="bg-teal-500 text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-teal-600 transition">
            Start Your Free Trial
          </button>
        </motion.div>
      </div>
    </section>
  )
}
