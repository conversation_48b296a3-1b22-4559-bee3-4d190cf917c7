'use client'
import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import { Check, X } from 'lucide-react'

const comparisonData = [
  { feature: "Dedicated Bookkeeper", duncanfly: true, others: true },
  { feature: "Monthly Financial Statements", duncanfly: true, others: true },
  { feature: "Expense Tracking", duncanfly: true, others: true },
  { feature: "Tax Preparation Support", duncanfly: true, others: false },
  { feature: "Same-Day Response", duncanfly: true, others: false },
  { feature: "Industry Expertise", duncanfly: true, others: false },
  { feature: "Custom Reporting", duncanfly: true, others: false },
  { feature: "CFO Advisory Services", duncanfly: true, others: false },
  { feature: "Unlimited Support", duncanfly: true, others: false },
  { feature: "Price Lock Guarantee", duncanfly: true, others: false }
]

export default function BenchAlternative() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  })

  return (
    <section ref={ref} className="py-20 gradient-bg">
      <div className="container mx-auto px-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl font-bold text-gray-800 mb-4">
            Looking for a Bench alternative?
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            DuncanFly's 20+ years in business make it the trusted solution to replace Bench Accounting.
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="bg-white rounded-2xl shadow-xl overflow-hidden max-w-4xl mx-auto"
        >
          <div className="grid grid-cols-3 bg-gray-50 p-6 font-bold text-center">
            <div>Features</div>
            <div className="text-teal-600">DuncanFly</div>
            <div className="text-gray-500">Others</div>
          </div>
          
          {comparisonData.map((item, index) => (
            <motion.div
              key={item.feature}
              initial={{ opacity: 0, x: -20 }}
              animate={inView ? { opacity: 1, x: 0 } : {}}
              transition={{ duration: 0.4, delay: index * 0.05 }}
              className="grid grid-cols-3 p-6 border-b border-gray-100 hover:bg-gray-50 transition"
            >
              <div className="font-medium text-gray-700">{item.feature}</div>
              <div className="text-center">
                {item.duncanfly ? (
                  <Check className="w-6 h-6 text-teal-500 mx-auto" />
                ) : (
                  <X className="w-6 h-6 text-gray-300 mx-auto" />
                )}
              </div>
              <div className="text-center">
                {item.others ? (
                  <Check className="w-6 h-6 text-gray-400 mx-auto" />
                ) : (
                  <X className="w-6 h-6 text-gray-300 mx-auto" />
                )}
              </div>
            </motion.div>
          ))}
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="text-center mt-12"
        >
          <p className="text-lg text-gray-600 mb-6">
            Join over 2,600 businesses that switched to DuncanFly
          </p>
          <button className="bg-teal-500 text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-teal-600 transition">
            Learn More
          </button>
        </motion.div>
      </div>
    </section>
  )
}
