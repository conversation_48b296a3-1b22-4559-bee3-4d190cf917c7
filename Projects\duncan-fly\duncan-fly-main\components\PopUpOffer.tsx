'use client'
import { useEffect, useState } from 'react'

export default function PopUpOffer() {
  const [show, setShow] = useState(false)

  useEffect(() => {
    const timer = setTimeout(() => setShow(true), 5000)
    return () => clearTimeout(timer)
  }, [])

  if (!show) return null

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40">
      <div className="bg-white rounded-2xl shadow-2xl p-8 max-w-md w-full relative glass-effect border border-teal-200">
        <button
          className="absolute top-4 right-4 text-gray-400 hover:text-teal-600 text-2xl"
          onClick={() => setShow(false)}
          aria-label="Close"
        >
          ×
        </button>
        <h2 className="text-2xl font-bold text-teal-600 mb-2 text-center">Unlock Free Bookkeeping Resources!</h2>
        <p className="text-gray-700 mb-4 text-center">
          Get instant access to our exclusive QuickBooks Online checklists, templates, and guides—plus a <span className="text-teal-600 font-semibold">free consultation</span> with a certified expert.
        </p>
        <form className="space-y-4">
          <input
            type="email"
            placeholder="Your email address"
            className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-teal-400"
            required
          />
          <button
            type="submit"
            className="w-full bg-teal-500 text-white py-3 rounded-full font-semibold hover:bg-teal-600 transition"
          >
            Get Free Resources & Consultation
          </button>
        </form>
        <p className="text-xs text-gray-500 mt-4 text-center">
          No spam. Unsubscribe anytime. See our <a href="/privacy" className="underline text-teal-600">Privacy Policy</a>.
        </p>
      </div>
    </div>
  )
}
