import Head from 'next/head'

interface SEOProps {
  title?: string
  description?: string
  keywords?: string
  ogImage?: string
  canonicalUrl?: string
}

export default function SEO({ 
  title = "DuncanFly Bookkeeping - Bookkeeping, Payroll, Tax & CFO Services for Small Business",
  description = "DuncanFly Bookkeeping provides monthly bookkeeping, payroll, tax preparation, financial reporting, cloud accounting setup, and CFO advisory for small businesses and entrepreneurs. Expert, cloud-based, and industry-specific solutions.",
  keywords = "bookkeeping, payroll, tax preparation, financial reporting, cloud accounting, CFO advisory, small business, DuncanFly",
  ogImage = "/images/og-image.jpg",
  canonicalUrl = "https://duncanfly.com"
}: SEOProps) {
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "AccountingService",
    "name": "DuncanFly Bookkeeping",
    "description": description,
    "url": canonicalUrl,
    "telephone": "******-567-8900",
    "address": {
      "@type": "PostalAddress",
      "addressLocality": "Your City",
      "addressRegion": "State",
      "postalCode": "12345",
      "addressCountry": "US"
    },
    "sameAs": [
      "https://facebook.com/duncanfly",
      "https://linkedin.com/company/duncanfly",
      "https://twitter.com/duncanfly"
    ]
  }

  return (
    <Head>
      <title>{title}</title>
      <meta name="description" content={description} />
      <meta name="keywords" content={keywords} />
      <meta name="viewport" content="width=device-width, initial-scale=1" />
      <link rel="canonical" href={canonicalUrl} />
      
      {/* Open Graph */}
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
      <meta property="og:image" content={ogImage} />
      <meta property="og:url" content={canonicalUrl} />
      <meta property="og:type" content="website" />
      
      {/* Twitter Card */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={title} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={ogImage} />
      
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />
    </Head>
  )
}
