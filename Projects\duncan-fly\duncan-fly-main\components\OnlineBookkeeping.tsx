'use client'
import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import Image from 'next/image'
import { Calendar, DollarSign, FileText, Users } from 'lucide-react'

const services = [
  {
    icon: Calendar,
    title: "Save $500 monthly",
    description: "Reclaim 20+ hours each month"
  },
  {
    icon: DollarSign,
    title: "Boost your business's survival rate by 313%",
    description: "With accurate financial data"
  },
  {
    icon: FileText,
    title: "Get the chatter and make better business decisions",
    description: "Clear financial insights"
  },
  {
    icon: Users,
    title: "Clear the clutter and make better business decisions",
    description: "Organized financial records"
  }
]

export default function OnlineBookkeeping() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  })

  return (
    <section ref={ref} className="py-20 bg-white">
      <div className="container mx-auto px-6">
        <div className="grid md:grid-cols-2 gap-12 items-center">
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={inView ? { opacity: 1, x: 0 } : {}}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-4xl font-bold text-gray-800 mb-6">
              Online Bookkeeping Services Built for Ecommerce, SaaS, Agencies, and More
            </h2>
            <p className="text-xl text-gray-600 mb-8">
              DuncanFly provides online bookkeeping services and full-service financial support 
              for thousands of businesses, organizations, and their advisors. Whether you need 
              catch-up bookkeeping, controller oversight, tax strategy, or CFO services, we offer 
              scalable solutions built for your business type. You also work with the right financial 
              team - so you can focus on what you do best.
            </p>
            
            <div className="grid grid-cols-2 gap-6">
              {services.map((service, index) => (
                <motion.div
                  key={service.title}
                  initial={{ opacity: 0, y: 20 }}
                  animate={inView ? { opacity: 1, y: 0 } : {}}
                  transition={{ duration: 0.4, delay: index * 0.1 }}
                  className="flex items-start space-x-3"
                >
                  <div className="w-12 h-12 bg-teal-100 rounded-lg flex items-center justify-center flex-shrink-0">
                    <service.icon className="w-6 h-6 text-teal-600" />
                  </div>
                  <div>
                    <h4 className="font-bold text-gray-800">{service.title}</h4>
                    <p className="text-sm text-gray-600">{service.description}</p>
                  </div>
                </motion.div>
              ))}
            </div>

            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="mt-8 bg-teal-500 text-white px-8 py-4 rounded-full text-lg font-semibold hover:bg-teal-600 transition"
            >
              How DuncanFly Works
            </motion.button>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={inView ? { opacity: 1, x: 0 } : {}}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="relative"
          >
            <div className="relative">
              <Image 
                src="https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=600" 
                alt="Business owner working" 
                width={600} 
                height={400} 
                className="rounded-2xl shadow-xl"
              />
              {/* Floating stat cards */}
              <motion.div
                animate={{ y: [0, -10, 0] }}
                transition={{ duration: 4, repeat: Infinity }}
                className="absolute -bottom-6 -left-6 bg-white rounded-xl shadow-lg p-4"
              >
                <p className="text-3xl font-bold text-teal-500">2,600+</p>
                <p className="text-gray-600">Happy Clients</p>
              </motion.div>
              <motion.div
                animate={{ y: [0, 10, 0] }}
                transition={{ duration: 3.5, repeat: Infinity }}
                className="absolute -top-6 -right-6 bg-white rounded-xl shadow-lg p-4"
              >
                <p className="text-3xl font-bold text-purple-500">98%</p>
                <p className="text-gray-600">Satisfaction Rate</p>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
