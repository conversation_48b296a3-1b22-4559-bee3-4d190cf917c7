'use client'
import React, { createContext, useContext, useState, ReactNode } from 'react'

interface Message {
  id: string
  text: string
  sender: 'user' | 'bot'
  timestamp: Date
}

interface ChatbotContextType {
  isOpen: boolean
  setIsOpen: (open: boolean) => void
  messages: Message[]
  addMessage: (text: string, sender: 'user' | 'bot') => void
  clearMessages: () => void
}

const ChatbotContext = createContext<ChatbotContextType | undefined>(undefined)

export function ChatbotProvider({ children }: { children: ReactNode }) {
  const [isOpen, setIsOpen] = useState(false)
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      text: "Hi! I'm here to help you with your bookkeeping needs. How can I assist you today?",
      sender: 'bot',
      timestamp: new Date()
    }
  ])

  const addMessage = (text: string, sender: 'user' | 'bot') => {
    const newMessage: Message = {
      id: Date.now().toString(),
      text,
      sender,
      timestamp: new Date()
    }
    setMessages(prev => [...prev, newMessage])
  }

  const clearMessages = () => {
    setMessages([{
      id: '1',
      text: "Hi! I'm here to help you with your bookkeeping needs. How can I assist you today?",
      sender: 'bot',
      timestamp: new Date()
    }])
  }

  return (
    <ChatbotContext.Provider value={{ isOpen, setIsOpen, messages, addMessage, clearMessages }}>
      {children}
    </ChatbotContext.Provider>
  )
}

export const useChatbot = () => {
  const context = useContext(ChatbotContext)
  if (!context) {
    throw new Error('useChatbot must be used within a ChatbotProvider')
  }
  return context
}
