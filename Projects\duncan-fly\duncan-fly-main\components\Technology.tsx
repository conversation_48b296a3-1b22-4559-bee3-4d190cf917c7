'use client'
import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import Image from 'next/image'

const technologies = [
  { name: "QuickBook<PERSON>", logo: "/images/quickbooks-logo.png" },
  { name: "<PERSON><PERSON>", logo: "/images/xero-logo.png" },
  { name: "Wave", logo: "/images/wave-logo.png" },
  { name: "FreshBooks", logo: "/images/freshbooks-logo.png" },
  { name: "<PERSON><PERSON>", logo: "/images/stripe-logo.png" },
  { name: "Square", logo: "/images/square-logo.png" }
]

export default function Technology() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  })

  return (
    <section ref={ref} className="py-20 bg-white">
      <div className="container mx-auto px-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl font-bold text-gray-800 mb-4">
            Technology-Driven. Expert-Led.
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            At DuncanFly, we harness best-in-class accounting technology to deliver seamless online 
            bookkeeping and tax filing services. Our platform integrates with your favorite tools, 
            providing a unified financial management experience.
          </p>
        </motion.div>

        <div className="grid md:grid-cols-2 gap-12 items-center">
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={inView ? { opacity: 1, x: 0 } : {}}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <h3 className="text-3xl font-bold text-gray-800 mb-6">
              Seamless Integrations
            </h3>
            <p className="text-gray-600 mb-6">
              Connect your entire financial ecosystem. DuncanFly integrates with leading 
              accounting software, payment processors, and business tools to provide 
              real-time financial insights.
            </p>
            <div className="grid grid-cols-3 gap-4 mb-8">
              {technologies.map((tech) => (
                <div key={tech.name} className="bg-gray-50 rounded-lg p-4 flex items-center justify-center">
                  <span className="text-gray-700 font-medium">{tech.name}</span>
                </div>
              ))}
            </div>
            <button className="bg-gray-800 text-white px-6 py-3 rounded-full hover:bg-gray-700 transition">
              View All Integrations
            </button>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={inView ? { opacity: 1, x: 0 } : {}}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="relative"
          >
            <div className="glass-effect rounded-2xl p-8">
              <Image 
                src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=600" 
                alt="Financial Analytics Dashboard" 
                width={600} 
                height={400} 
                className="rounded-lg shadow-lg"
              />
            </div>
            {/* Floating elements */}
            <motion.div
              animate={{ y: [0, -10, 0] }}
              transition={{ duration: 3, repeat: Infinity }}
              className="absolute -top-4 -right-4 bg-teal-500 text-white px-4 py-2 rounded-full shadow-lg"
            >
              Real-time Sync
            </motion.div>
            <motion.div
              animate={{ y: [0, 10, 0] }}
              transition={{ duration: 4, repeat: Infinity }}
              className="absolute -bottom-4 -left-4 bg-purple-500 text-white px-4 py-2 rounded-full shadow-lg"
            >
              Bank-level Security
            </motion.div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
