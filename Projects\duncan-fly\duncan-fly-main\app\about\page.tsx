import Header from '../../components/Header'
import Footer from '../../components/Footer'
import SEO from '../../components/SEO'
import Breadcrumbs from '../../components/Breadcrumbs'

export default function About() {
  return (
    <>
      <SEO
        title="About DuncanFly Bookkeeping | Trusted Bookkeeping Experts"
        description="Learn about DuncanFly Bookkeeping, our mission, values, and 20+ years of experience helping small businesses succeed with expert bookkeeping, payroll, and tax services."
        keywords="about <PERSON><PERSON><PERSON>, bookkeeping experts, small business, cloud accounting, our team"
        canonicalUrl="https://duncanfly.com/about"
      />
      <Header />
      <section className="gradient-bg py-20">
        <div className="container mx-auto px-6">
          <div className="glass-effect rounded-2xl p-10 shadow-2xl max-w-3xl mx-auto">
            <h1 className="text-4xl font-bold mb-6 text-gray-800">About DuncanFly Bookkeeping</h1>
            <p className="text-lg text-gray-700 mb-4">
              DuncanFly Bookkeeping provides expert financial solutions for entrepreneurs and small businesses. 
              Our mission is to empower business owners with accurate, timely, and insightful bookkeeping services, 
              leveraging the latest technology and a team of experienced professionals.
            </p>
            <p className="text-lg text-gray-700 mb-4">
              With over <span className="text-teal-600 font-semibold">20 years of experience</span>, DuncanFly is trusted by thousands of businesses to manage their finances, 
              streamline operations, and support growth. We are committed to transparency, integrity, and exceptional client service.
            </p>
            <div className="mt-8">
              <a href="/services" className="inline-block bg-teal-500 text-white px-8 py-3 rounded-full text-lg font-semibold hover:bg-teal-600 transition">
                Explore Our Services
              </a>
            </div>
          </div>
        </div>
      </section>
      <Footer />
    </>
  )
}
