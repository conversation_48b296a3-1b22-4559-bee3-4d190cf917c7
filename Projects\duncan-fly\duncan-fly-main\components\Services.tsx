'use client'
import { motion } from 'framer-motion'
import { useInView } from 'react-intersection-observer'
import { Users, TrendingUp, Shield } from 'lucide-react'

const services = [
  {
    icon: Users,
    title: "People",
    description: "Connect with experienced bookkeepers who understand small business needs. Our team brings industry expertise, personalized attention, and strategic financial guidance to help your business thrive.",
    features: ["Dedicated account manager", "Industry-specific expertise", "24/7 support access"]
  },
  {
    icon: TrendingUp,
    title: "Process",
    description: "Streamline your financial workflow with our proven processes. We handle everything from daily transactions to monthly reporting, ensuring accuracy and compliance while saving you valuable time.",
    features: ["Automated workflows", "Real-time reporting", "Monthly reconciliation"]
  },
  {
    icon: Shield,
    title: "Technology",
    description: "Leverage cutting-edge technology to transform your bookkeeping. Our cloud-based solutions provide real-time insights, secure data storage, and seamless integration with your favorite business tools.",
    features: ["Cloud-based platform", "Bank integration", "Mobile access"]
  }
]

export default function Services() {
  const [ref, inView] = useInView({
    triggerOnce: true,
    threshold: 0.1
  })

  return (
    <section ref={ref} className="py-20 bg-white">
      <div className="container mx-auto px-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={inView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl font-bold text-gray-800 mb-4">
            THE FIRST STEP
          </h2>
          <h3 className="text-3xl text-gray-700">
            The online bookkeeping services you need.
          </h3>
          <p className="text-xl text-gray-600 mt-4 max-w-3xl mx-auto">
            Over 2,600 innovative entrepreneurs have relied on DuncanFly as the financial foundation for their business. 
            Entrepreneurs trust us to do the people, accommodate the best accounting technologies, and implement a sustainable financial process.
          </p>
        </motion.div>

        <div className="grid md:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <motion.div
              key={service.title}
              initial={{ opacity: 0, y: 30 }}
              animate={inView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.6, delay: index * 0.2 }}
              className="bg-gray-50 rounded-2xl p-8 hover:shadow-xl transition-shadow duration-300"
            >
              <div className="w-16 h-16 bg-teal-100 rounded-full flex items-center justify-center mb-6">
                <service.icon className="w-8 h-8 text-teal-600" />
              </div>
              <h4 className="text-2xl font-bold text-gray-800 mb-4">{service.title}</h4>
              <p className="text-gray-600 mb-6">{service.description}</p>
              <ul className="space-y-2">
                {service.features.map((feature) => (
                  <li key={feature} className="flex items-center text-gray-700">
                    <div className="w-2 h-2 bg-teal-500 rounded-full mr-3"></div>
                    {feature}
                  </li>
                ))}
              </ul>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}
