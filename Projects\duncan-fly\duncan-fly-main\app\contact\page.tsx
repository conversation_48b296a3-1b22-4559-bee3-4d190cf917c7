import Header from '../../components/Header'
import Footer from '../../components/Footer'
import SEO from '../../components/SEO'
import Breadcrumbs from '../../components/Breadcrumbs'

export default function Contact() {
  return (
    <>
      <SEO
        title="Contact DuncanFly Bookkeeping | Get in Touch"
        description="Contact DuncanFly Bookkeeping for expert bookkeeping, payroll, and tax support. Reach out for a free consultation or to ask a bookkeeping question."
        keywords="contact DuncanFly, bookkeeping support, free consultation, small business accounting"
        canonicalUrl="https://duncanfly.com/contact"
      />
      <Header />
      <Breadcrumbs />
      <section className="gradient-bg py-20">
        <div className="container mx-auto px-6">
          <div className="glass-effect rounded-2xl p-10 shadow-2xl max-w-3xl mx-auto">
            <h1 className="text-4xl font-bold mb-6 text-gray-800">Contact Us</h1>
            <p className="text-lg text-gray-700 mb-8">
              Have questions or want to get started? Fill out the form below or reach us directly at <a href="mailto:<EMAIL>" className="text-teal-600 underline"><EMAIL></a> or <a href="tel:+***********" className="text-teal-600 underline">(*************</a>.
            </p>
            <form className="bg-white rounded-xl shadow-md p-8 space-y-6">
              <div>
                <label className="block text-gray-700 font-semibold mb-2" htmlFor="name">Name</label>
                <input className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-teal-400" type="text" id="name" name="name" required />
              </div>
              <div>
                <label className="block text-gray-700 font-semibold mb-2" htmlFor="email">Email</label>
                <input className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-teal-400" type="email" id="email" name="email" required />
              </div>
              <div>
                <label className="block text-gray-700 font-semibold mb-2" htmlFor="message">Message</label>
                <textarea className="w-full border border-gray-300 rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-teal-400" id="message" name="message" rows={5} required />
              </div>
              <button type="submit" className="w-full bg-teal-500 text-white py-3 rounded-full font-semibold hover:bg-teal-600 transition">
                Send Message
              </button>
            </form>
            <div className="mt-12 text-gray-700">
              <h2 className="text-2xl font-bold mb-2 text-gray-800">DuncanFly Bookkeeping</h2>
              <p>123 Main Street, Suite 100<br />Your City, State 12345</p>
              <p className="mt-2">Email: <a href="mailto:<EMAIL>" className="text-teal-600 underline"><EMAIL></a></p>
              <p>Phone: <a href="tel:+***********" className="text-teal-600 underline">(*************</a></p>
            </div>
          </div>
        </div>
      </section>
      <Footer />
    </>
  )
}
