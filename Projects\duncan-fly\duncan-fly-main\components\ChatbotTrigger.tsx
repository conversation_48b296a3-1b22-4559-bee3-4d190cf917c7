'use client'
import { motion } from 'framer-motion'
import { MessageCircle } from 'lucide-react'
import { useChatbot } from '../contexts/ChatbotContext'

export default function ChatbotTrigger() {
  const { isOpen, setIsOpen } = useChatbot()

  if (isOpen) return null

  return (
    <motion.button
      initial={{ scale: 0 }}
      animate={{ scale: 1 }}
      whileHover={{ scale: 1.1 }}
      whileTap={{ scale: 0.9 }}
      onClick={() => setIsOpen(true)}
      className="fixed bottom-6 right-6 w-16 h-16 bg-gradient-to-r from-teal-500 to-teal-600 rounded-full shadow-lg flex items-center justify-center z-40"
    >
      <MessageCircle className="w-8 h-8 text-white" />
      <span className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full animate-pulse"></span>
    </motion.button>
  )
}
