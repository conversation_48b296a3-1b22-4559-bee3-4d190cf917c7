import './globals.css'
import type { Metadata } from 'next'
import { ChatbotProvider } from '../contexts/ChatbotContext'
import Chatbot from '../components/Chatbot'
import ChatbotTrigger from '../components/ChatbotTrigger'

export const metadata: Metadata = {
  title: 'DuncanFly Bookkeeping - Expert Financial Solutions for Small Business',
  description: 'Professional bookkeeping services for entrepreneurs and small businesses. Streamline your finances with cloud-based solutions, expert support, and transparent pricing.',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body>
        <ChatbotProvider>
          {children}
          <Chatbot />
          <ChatbotTrigger />
        </ChatbotProvider>
      </body>
    </html>
  )
}
