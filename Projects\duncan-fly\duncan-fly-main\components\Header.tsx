'use client'
import { useState, useEffect } from 'react'
import Link from 'next/link'
import { motion } from 'framer-motion'
import { Menu, X } from 'lucide-react'
import { useChatbot } from '../contexts/ChatbotContext'

export default function Header() {
  const [isScrolled, setIsScrolled] = useState(false)
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const { setIsOpen } = useChatbot()

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10)
    }
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  return (
    <motion.header
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      className={`fixed top-0 w-full z-50 transition-all duration-300 ${
        isScrolled ? 'bg-white shadow-lg' : 'bg-transparent'
      }`}
    >
      <nav className="container mx-auto px-6 py-4">
        <div className="flex justify-between items-center">
          <Link href="/" className="flex items-center space-x-2">
            <div className="w-10 h-10 bg-gradient-to-r from-teal-400 to-teal-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-xl">DF</span>
            </div>
            <span className="text-2xl font-bold text-gray-800">DuncanFly</span>
          </Link>

          {/* Desktop Menu */}
          <div className="hidden md:flex flex-1 justify-end items-center space-x-8">
            <Link href="/services" className="text-gray-700 hover:text-teal-500 transition">Services</Link>
            <Link href="/resources" className="text-gray-700 hover:text-teal-500 transition">Resources</Link>
            <Link href="/about" className="text-gray-700 hover:text-teal-500 transition">About Us</Link>
            <Link href="/blog" className="text-gray-700 hover:text-teal-500 transition">Blog</Link>
            <button
              onClick={() => setIsOpen(true)}
              className="bg-teal-500 text-white px-6 py-2 rounded-full hover:bg-teal-600 transition"
            >
              Talk to an Expert
            </button>
          </div>

          {/* Mobile Menu Button */}
          <button
            className="md:hidden"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          >
            {isMobileMenuOpen ? <X size={24} /> : <Menu size={24} />}
          </button>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="md:hidden mt-4 pb-4"
          >
            <div className="flex flex-col space-y-4">
              <Link href="/services" className="text-gray-700">Services</Link>
              <Link href="/resources" className="text-gray-700">Resources</Link>
              <Link href="/about" className="text-gray-700">About Us</Link>
              <Link href="/blog" className="text-gray-700">Blog</Link>
              <button
                onClick={() => setIsOpen(true)}
                className="bg-teal-500 text-white px-6 py-2 rounded-full hover:bg-teal-600 transition"
              >
                Talk to an Expert
              </button>
            </div>
          </motion.div>
        )}
      </nav>
    </motion.header>
  )
}
